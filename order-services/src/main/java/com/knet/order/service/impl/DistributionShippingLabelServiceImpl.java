package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.model.dto.third.req.KnetCreateShipment;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.entity.SysShippingLabel;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16 10:30
 * @description: 分配物流标签服务实现
 */
@Slf4j
@Service
public class DistributionShippingLabelServiceImpl implements IDistributionShippingLabelService {

    @Resource
    private ISysOrderItemService iSysOrderItemService;
    @Resource
    private ISysOrderGroupService iSysOrderGroupService;
    @Resource
    private ApiUserServiceProvider apiUserServiceProvider;
    @Resource
    private ISysShippingLabelService iSysShippingLabelService;
    @Resource
    private ISysShippingItemRelService iSysShippingItemRelService;
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private ISysOrderProcessService iSysOrderProcessService;
    @Resource
    private OrderProducer orderProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributionShippingLabel(String prentOrderId) {
        SysOrderGroup orderGroup = iSysOrderGroupService.getOrderGroupByOrderId(prentOrderId);
        if (orderGroup == null) {
            log.error("父订单不存在，无法分配物流标签: prentOrderId={}", prentOrderId);
            return;
        }
        Long addressId = orderGroup.getAddressId();
        if (addressId == null) {
            log.error("订单缺少地址信息，无法分配物流标签: prentOrderId={}", prentOrderId);
            return;
        }
        // 获取收货地址信息
        UserAddressDtoResp addressInfo = null;
        //todo 异常订单处理机制 没有物流单号的，人工介入处置
        try {
            HttpResult<UserAddressDtoResp> addressResult = apiUserServiceProvider.getAddressById(addressId);
            if (addressResult.getData() == null) {
                log.error("获取地址信息失败: addressId={}", addressId);
                return;
            }
            addressInfo = addressResult.getData();
            log.info("获取地址信息成功: addressId={}, country={}, fullName={}", addressId, addressInfo.getCountry(), addressInfo.getFullName());
        } catch (Exception e) {
            log.error("调用用户服务获取地址信息异常: addressId={}, error={}", addressId, e.getMessage(), e);
            return;
        }
        // 从KG获取物流标签 根据仓库信息，同一个仓库10件商品一个shipping_label
        List<SysOrderItem> items = iSysOrderItemService.getOrderItemsByPrentOrderId(prentOrderId);
        log.info("根据父订单ID获取订单明细: prentOrderId={}, count={}", prentOrderId, items.size());
        if (items.isEmpty()) {
            log.warn("订单明细为空，无法分配物流标签: prentOrderId={}", prentOrderId);
            return;
        }
        // 按仓库分组
        Map<String, List<SysOrderItem>> warehouseGroups = new HashMap<>(12);
        for (SysOrderItem item : items) {
            String warehouse = item.getWarehouse();
            if (warehouse == null || warehouse.isEmpty()) {
                log.warn("订单项缺少仓库信息，无法分配物流标签: itemId={}", item.getItemId());
                continue;
            }
            warehouseGroups.computeIfAbsent(warehouse, k -> new ArrayList<>()).add(item);
        }
        // 每个仓库每10件商品分配一个物流标签
        for (Map.Entry<String, List<SysOrderItem>> entry : warehouseGroups.entrySet()) {
            String warehouse = entry.getKey();
            List<SysOrderItem> warehouseItems = entry.getValue();
            // 处理同一仓库下的商品
            processWarehouseItems(prentOrderId, warehouse, warehouseItems, addressInfo, addressId);
        }
        // 智能更新订单状态（根据物流标签分配情况自动判断）
        try {
            boolean result = iSysOrderProcessService.smartUpdateOrderStatus(prentOrderId);
            if (result) {
                // 发送订单通知消息到notification-service
                sendOrderNotificationMessages(prentOrderId);
                log.info("订单状态已根据物流标签分配情况自动更新: prentOrderId={}", prentOrderId);
            } else {
                log.warn("订单状态更新失败: prentOrderId={}", prentOrderId);
            }
        } catch (Exception e) {
            log.error("更新订单状态时发生异常: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
        }
        log.info("物流标签分配完成: prentOrderId={}", prentOrderId);
    }

    /**
     * 处理同一仓库下的商品分配物流标签
     *
     * @param parentOrderId  父订单ID
     * @param warehouse      仓库
     * @param warehouseItems 仓库商品
     * @param addressInfo    地址信息
     * @param addressId      地址ID
     */
    private void processWarehouseItems(String parentOrderId, String warehouse, List<SysOrderItem> warehouseItems,
                                       UserAddressDtoResp addressInfo, Long addressId) {
        int currentItemCount = 0;
        List<SysOrderItem> currentBatch = new ArrayList<>();
        for (SysOrderItem item : warehouseItems) {
            int count = item.getCount();
            currentBatch.add(item);
            currentItemCount += count;
            // 如果当前批次已满或者是最后一个商品，获取物流标签并保存数据
            if (currentItemCount >= 10 || isLastItem(warehouseItems, item)) {
                // 从KG系统获取一个物流标签
                KnetShopLabelCenterVo shippingLabelFromKg = getShippingLabelFromKg(addressInfo, warehouse, (long) warehouseItems.size());
                if (BeanUtil.isNotEmpty(shippingLabelFromKg)) {
                    SysShippingLabel shippingLabel = iSysShippingLabelService.createShippingLabel(SysShippingLabel.create(shippingLabelFromKg));
                    for (SysOrderItem batchItem : currentBatch) {
                        iSysShippingItemRelService.saveShippingItemRel(shippingLabel.getId(), batchItem.getItemId());
                        log.info("为商品分配物流标签: itemId={}, sku={}", batchItem.getItemId(), batchItem.getSku());
                    }
                    // 重置当前批次和计数
                    currentBatch = new ArrayList<>();
                    currentItemCount = 0;
                } else {
                    log.error("获取物流标签失败: warehouse={}", warehouse);
                    break;
                }
            }
        }
    }

    /**
     * 判断是否是最后一个商品
     *
     * @param items       商品列表
     * @param currentItem 当前商品
     * @return 是否是最后一个商品
     */
    private boolean isLastItem(List<SysOrderItem> items, SysOrderItem currentItem) {
        return items.indexOf(currentItem) == items.size() - 1;
    }

    /**
     * 从KG系统获取物流标签
     *
     * @param addressInfo 收件地址信息
     * @param warehouse   发货仓库
     * @param count       包裹数
     * @return 物流标签编码
     */
    private KnetShopLabelCenterVo getShippingLabelFromKg(UserAddressDtoResp addressInfo, String warehouse, Long count) {
        try {
            log.info("向KG系统请求物流标签");
            // 调用KG系统API获取物流标签
            KnetCreateShipment shipment = KnetCreateShipment.create(addressInfo, warehouse, count);
            KnetShopLabelCenterVo shippingLabel = thirdApiService.getShippingLabel(shipment);
            log.info("成功从KG系统获取物流标签: label={}", shippingLabel);
            return shippingLabel;
        } catch (Exception e) {
            log.error("从KG系统获取物流标签异常: error={}", e.getMessage(), e);
            // 异常情况下，返回null表示获取失败
            return null;
        }
    }

    /**
     * 发送订单通知消息到notification-service
     * 主订单包含几个子订单发送几条消息，确保消息不重复发送
     *
     * @param parentOrderId 父订单ID
     */
    private void sendOrderNotificationMessages(String parentOrderId) {
        try {
            // 获取所有子订单项
            List<SysOrderItem> orderItems = iSysOrderItemService.getOrderItemsByPrentOrderId(parentOrderId);
            if (orderItems.isEmpty()) {
                log.warn("未找到子订单项，无法发送订单通知: parentOrderId={}", parentOrderId);
                return;
            }
            for (SysOrderItem orderItem : orderItems) {
                // 使用Redis确保消息不重复发送
                String messageKey = String.format("ORDER_NOTIFICATION_SENT:%s", orderItem.getItemNo());
                if (!RedisCacheUtil.setIfAbsent(messageKey, "SENT", 3600)) {
                    log.info("订单通知消息已发送过，跳过: itemNo={}", orderItem.getItemNo());
                    continue;
                }
                // 创建订单通知消息
                NotificationMessage notificationMessage = NotificationMessage.createOrderNotice(
                        parentOrderId,
                        orderItem.getItemNo(),
                        orderItem.getSku()
                );
                // 发送消息
                orderProducer.sendOrderNotificationMessage(notificationMessage);
                log.info("已发送订单通知消息: parentOrderId={}, itemNo={}, sku={}",
                        parentOrderId, orderItem.getItemNo(), orderItem.getSku());
            }
        } catch (Exception e) {
            log.error("发送订单通知消息异常: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
        }
    }
}
