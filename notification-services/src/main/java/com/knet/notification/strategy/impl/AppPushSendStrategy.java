package com.knet.notification.strategy.impl;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.strategy.INotificationSendStrategy;
import com.knet.notification.system.utils.WebhookSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: APP推送发送策略实现
 */
@Slf4j
@Component
public class AppPushSendStrategy implements INotificationSendStrategy {

    @Resource
    private WebhookSender webhookSender;

    @Override
    public MessageChannel getSupportedChannel() {
        return MessageChannel.APP_PUSH;
    }

    @Override
    public boolean sendNotification(SysMessage sysMessage) {
        try {
            // 解析消息内容，提取订单信息
            String content = sysMessage.getContent();
            String[] parts = content.split("\\|");
            if (parts.length < 3) {
                log.error("APP_PUSH消息格式错误，无法解析订单信息: {}", content);
                return false;
            }
            String orderNo = parts[0];
            String orderSubNo = parts[1];
            String sku = parts[2];
            boolean sendResult = webhookSender.sendAppPushMessage(orderNo, orderSubNo, sku);
            if (sendResult) {
                log.info("APP推送发送成功: {}", sysMessage);
            } else {
                log.error("APP推送发送失败: {}", sysMessage);
            }
            return sendResult;
        } catch (Exception e) {
            log.error("APP推送发送异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
