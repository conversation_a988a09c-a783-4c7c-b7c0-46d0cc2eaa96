package com.knet.notification.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/8/15 10:00
 * @description: Webhook配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "webhook")
public class WebhookProperties {
    /**
     * APP_PUSH通道的webhook地址
     */
    private String appPushUrl = "*********************************************************************************";
}
