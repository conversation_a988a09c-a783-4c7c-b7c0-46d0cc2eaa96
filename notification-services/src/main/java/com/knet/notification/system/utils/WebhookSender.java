package com.knet.notification.system.utils;

import com.alibaba.fastjson2.JSON;
import com.knet.notification.system.config.WebhookProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/15 10:05
 * @description: Webhook发送工具
 */
@Slf4j
@Component
public class WebhookSender {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WebhookProperties webhookProperties;

    /**
     * 发送APP_PUSH消息到Slack webhook
     *
     * @param orderNo    订单号
     * @param orderSubNo 子订单号
     * @param sku        商品SKU
     * @return 是否发送成功
     */
    public boolean sendAppPushMessage(String orderNo, String orderSubNo, String sku) {
        try {
            // 构建Slack消息格式
            Map<String, Object> message = new HashMap<>();
            Map<String, Object> block = new HashMap<>();
            Map<String, Object> text = new HashMap<>();
            
            block.put("type", "section");
            text.put("type", "mrkdwn");
            text.put("text", String.format("Order Notification\n>OrderNo: %s\n>OrderSubNo: %s\n>SKU: %s\n>", 
                    orderNo, orderSubNo, sku));
            block.put("text", text);
            
            message.put("blocks", List.of(block));

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(message), headers);

            log.info("发送APP_PUSH消息到webhook: orderNo={}, orderSubNo={}, sku={}", orderNo, orderSubNo, sku);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    webhookProperties.getAppPushUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("APP_PUSH消息发送成功: orderNo={}, orderSubNo={}, sku={}", orderNo, orderSubNo, sku);
                return true;
            } else {
                log.error("APP_PUSH消息发送失败: orderNo={}, orderSubNo={}, sku={}, status={}", 
                        orderNo, orderSubNo, sku, response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("发送APP_PUSH消息异常: orderNo={}, orderSubNo={}, sku={}, error={}", 
                    orderNo, orderSubNo, sku, e.getMessage(), e);
            return false;
        }
    }
}
