package com.knet.notification.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.common.enums.MessageStatus;
import com.knet.common.exception.ServiceException;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.service.INotificationService;
import com.knet.notification.service.ISysMessageDeliveryService;
import com.knet.notification.service.ISysMessageService;
import com.knet.notification.system.utils.GmailSender;
import com.knet.notification.system.utils.WebhookSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.knet.common.constants.NotificationConstants.KNET_B2B_ORDER_NOTIFICATION_SUB;

/**
 * <AUTHOR>
 * @date 2025/6/23 10:01
 * @description: 通知服务实现
 */
@Slf4j
@Service
public class NotificationServiceImpl implements INotificationService {
    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private GmailSender gmailSender;
    @Resource
    private ISysMessageDeliveryService sysMessageDeliveryService;
    @Resource
    private WebhookSender webhookSender;

    @Override
    public void sendNotification(NotificationMessage notificationMessage) {
        SysMessage saveMessage = sysMessageService.saveMessage(notificationMessage);
        if (BeanUtil.isNotEmpty(saveMessage)) {
            try {
                switch (notificationMessage.getDefaultChannel()) {
                    case EMAIL -> sendEmail(saveMessage);
                    case APP_PUSH -> sendAppPush(saveMessage);
                    default -> throw new ServiceException("不支持的通知类型");
                }
            } catch (Exception e) {
                log.error("发送通知失败: {}", e.getMessage(), e);
            }
        }
    }

    private void sendEmail(SysMessage sysMessage) throws Exception {
        boolean sendText = gmailSender.sendText(sysMessage.getReceiverId(), KNET_B2B_ORDER_NOTIFICATION_SUB, sysMessage.getContent());
        if (sendText) {
            sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.SUCCESS);
            log.info("发送通知成功: {}", sysMessage);
        } else {
            sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.FAILED);
            log.error("发送通知 失败: {}", sysMessage);
        }
    }

    private void sendAppPush(SysMessage sysMessage) throws Exception {
        // 解析消息内容，提取订单信息
        String content = sysMessage.getContent();
        String[] parts = content.split("\\|");
        if (parts.length >= 3) {
            String orderNo = parts[0];
            String orderSubNo = parts[1];
            String sku = parts[2];

            boolean sendResult = webhookSender.sendAppPushMessage(orderNo, orderSubNo, sku);
            if (sendResult) {
                sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.SUCCESS);
                log.info("发送APP_PUSH通知成功: {}", sysMessage);
            } else {
                sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.FAILED);
                log.error("发送APP_PUSH通知失败: {}", sysMessage);
            }
        } else {
            log.error("APP_PUSH消息格式错误，无法解析订单信息: {}", content);
            sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), sysMessage.getDefaultChannel(), MessageStatus.FAILED);
        }
    }
}
